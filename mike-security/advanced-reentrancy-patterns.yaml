rules:
  - id: read-only-reentrancy-vulnerability
    patterns:
      - pattern-inside: |
          function $FUNC(...) external view returns (...) {
            ...
          }
      - pattern-either:
          - pattern: |
              uint256 $BALANCE = $TOKEN.balanceOf($ADDRESS);
          - pattern: |
              (uint256 $RESERVE0, uint256 $RESERVE1, ) = $PAIR.getReserves();
          - pattern: |
              uint256 $TOTAL_SUPPLY = $TOKEN.totalSupply();
    message: "View function '$FUNC' reads external state that could be manipulated during reentrancy. Consider using reentrancy guards or cached values."
    severity: WARNING
    languages: [solidity]
    metadata:
      category: security
      technology: solidity
      description: "Read-only reentrancy can occur when view functions read manipulated state during ongoing transactions."

  - id: cross-function-reentrancy
    patterns:
      - pattern-either:
          - pattern: |
              function $FUNC1(...) external {
                ...
                $EXTERNAL_CALL;
                ...
                $STATE = $VALUE;
                ...
              }
          - pattern: |
              function $FUNC2(...) external {
                ...
                require($STATE == $EXPECTED, ...);
                ...
              }
      - pattern-not-inside: |
          modifier nonReentrant {
            ...
          }
    message: "Functions '$FUNC1' and '$FUNC2' may be vulnerable to cross-function reentrancy. Consider using global reentrancy guards."
    severity: WARNING
    languages: [solidity]
    metadata:
      category: security
      technology: solidity
      description: "Cross-function reentrancy occurs when one function's state changes affect another function's execution."

  - id: callback-reentrancy-erc721
    patterns:
      - pattern-inside: |
          function $FUNC(...) external {
            ...
          }
      - pattern: |
          $NFT.safeTransferFrom($FROM, $TO, $TOKEN_ID);
          ...
          $STATE = $NEW_VALUE;
    message: "ERC721 safeTransferFrom followed by state changes. The onERC721Received callback can reenter before state updates."
    severity: ERROR
    languages: [solidity]
    metadata:
      category: security
      technology: solidity
      description: "ERC721 safe transfers trigger callbacks that can reenter the contract before state changes are complete."

  - id: callback-reentrancy-erc1155
    patterns:
      - pattern-inside: |
          function $FUNC(...) external {
            ...
          }
      - pattern-either:
          - pattern: |
              $NFT.safeTransferFrom($FROM, $TO, $ID, $AMOUNT, $DATA);
              ...
              $STATE = $NEW_VALUE;
          - pattern: |
              $NFT.safeBatchTransferFrom($FROM, $TO, $IDS, $AMOUNTS, $DATA);
              ...
              $STATE = $NEW_VALUE;
    message: "ERC1155 safe transfer followed by state changes. The callback can reenter before state updates."
    severity: ERROR
    languages: [solidity]
    metadata:
      category: security
      technology: solidity
      description: "ERC1155 safe transfers trigger callbacks that can reenter the contract before state changes are complete."

  - id: reentrancy-via-fallback-function
    patterns:
      - pattern-inside: |
          function $FUNC(...) external {
            ...
          }
      - pattern-either:
          - pattern: |
              payable($ADDRESS).transfer($AMOUNT);
              ...
              $STATE = $NEW_VALUE;
          - pattern: |
              payable($ADDRESS).send($AMOUNT);
              ...
              $STATE = $NEW_VALUE;
          - pattern: |
              $ADDRESS.call{value: $AMOUNT}("");
              ...
              $STATE = $NEW_VALUE;
    message: "ETH transfer to external address followed by state changes. The recipient's fallback function can reenter."
    severity: ERROR
    languages: [solidity]
    metadata:
      category: security
      technology: solidity
      description: "ETH transfers can trigger fallback functions that may reenter the contract."

  - id: reentrancy-via-token-hooks
    patterns:
      - pattern-inside: |
          function $FUNC(...) external {
            ...
          }
      - pattern-either:
          - pattern: |
              $TOKEN.transfer($TO, $AMOUNT);
              ...
              $BALANCE[$USER] = $NEW_BALANCE;
          - pattern: |
              $TOKEN.transferFrom($FROM, $TO, $AMOUNT);
              ...
              $ALLOWANCE[$OWNER][$SPENDER] = $NEW_ALLOWANCE;
    message: "Token transfer followed by state changes. Tokens with hooks can reenter during transfer."
    severity: WARNING
    languages: [solidity]
    metadata:
      category: security
      technology: solidity
      description: "Some tokens implement hooks that can reenter the contract during transfers."

  - id: reentrancy-in-loop
    patterns:
      - pattern: |
          for (uint256 $I = 0; $I < $LENGTH; $I++) {
            ...
            $EXTERNAL_CALL;
            ...
            $STATE[$I] = $VALUE;
            ...
          }
    message: "External call in loop followed by state changes. Each iteration can be reentered, causing inconsistent state."
    severity: ERROR
    languages: [solidity]
    metadata:
      category: security
      technology: solidity
      description: "Reentrancy in loops can cause partial state updates and inconsistent contract state."

  - id: reentrancy-via-delegatecall
    patterns:
      - pattern-inside: |
          function $FUNC(...) external {
            ...
          }
      - pattern: |
          $TARGET.delegatecall($DATA);
          ...
          $STATE = $NEW_VALUE;
    message: "delegatecall followed by state changes. The called contract can modify state and reenter."
    severity: ERROR
    languages: [solidity]
    metadata:
      category: security
      technology: solidity
      description: "delegatecall executes code in the current contract's context and can modify state before reentering."

  - id: reentrancy-guard-bypass
    patterns:
      - pattern-either:
          - pattern: |
              modifier $GUARD {
                require(!$LOCKED, ...);
                $LOCKED = true;
                _;
                $LOCKED = false;
              }
          - pattern: |
              modifier $GUARD {
                $COUNTER++;
                require($COUNTER == 1, ...);
                _;
                $COUNTER--;
              }
      - pattern-not-inside: |
          modifier $GUARD {
            require(!$LOCKED, ...);
            $LOCKED = true;
            _;
            delete $LOCKED;
          }
    message: "Reentrancy guard implementation may be bypassable. Consider using OpenZeppelin's ReentrancyGuard."
    severity: WARNING
    languages: [solidity]
    metadata:
      category: security
      technology: solidity
      description: "Custom reentrancy guards should be carefully implemented to avoid bypass vulnerabilities."

  - id: missing-reentrancy-guard-on-payable
    patterns:
      - pattern: |
          function $FUNC(...) external payable {
            ...
            $EXTERNAL_CALL;
            ...
          }
      - pattern-not-inside: |
          modifier nonReentrant {
            ...
          }
      - pattern-not: |
          function $FUNC(...) external payable {
            require(!$LOCKED, ...);
            ...
          }
    message: "Payable function '$FUNC' makes external calls without reentrancy protection. ETH transfers can trigger reentrancy."
    severity: WARNING
    languages: [solidity]
    metadata:
      category: security
      technology: solidity
      description: "Payable functions that make external calls should have reentrancy protection."

  - id: state-dependent-external-call
    patterns:
      - pattern-inside: |
          function $FUNC(...) external {
            ...
          }
      - pattern: |
          if ($STATE == $VALUE) {
            $EXTERNAL_CALL;
          }
          ...
          $STATE = $NEW_VALUE;
    message: "External call depends on state that is modified later. Reentrancy can exploit this state dependency."
    severity: WARNING
    languages: [solidity]
    metadata:
      category: security
      technology: solidity
      description: "State-dependent external calls can be exploited through reentrancy to bypass intended logic."

  - id: reentrancy-via-create2
    patterns:
      - pattern-inside: |
          function $FUNC(...) external {
            ...
          }
      - pattern: |
          address $CONTRACT = Clones.cloneDeterministic($IMPLEMENTATION, $SALT);
          $INTERFACE($CONTRACT).$METHOD(...);
          ...
          $STATE = $NEW_VALUE;
    message: "Contract creation followed by immediate call and state changes. The created contract can reenter."
    severity: WARNING
    languages: [solidity]
    metadata:
      category: security
      technology: solidity
      description: "Newly created contracts can reenter the creating contract if called immediately after creation."
