rules:
  - id: incorrect-state-update-order
    patterns:
      - pattern-either:
          # Specific pattern for token transfers followed by balance updates
          - pattern: |
              function $FUNC(...) $MODIFIERS {
                ...
                $TOKEN.transfer($TO, $AMOUNT);
                ...
                $BALANCE[$USER] = $BALANCE[$USER] - $AMOUNT;
                ...
              }
          - pattern: |
              function $FUNC(...) $MODIFIERS {
                ...
                $TOKEN.transferFrom($FROM, $TO, $AMOUNT);
                ...
                $BALANCE[$USER] = $BALANCE[$USER] - $AMOUNT;
                ...
              }
          # External calls with value followed by state updates
          - pattern: |
              function $FUNC(...) $MODIFIERS {
                ...
                $ADDR.call{value: $VALUE}($DATA);
                ...
                $STATE = $NEW_VALUE;
                ...
              }
          - pattern: |
              function $FUNC(...) $MODIFIERS {
                ...
                $ADDR.send($VALUE);
                ...
                $STATE = $NEW_VALUE;
                ...
              }
          - pattern: |
              function $FUNC(...) $MODIFIERS {
                ...
                $ADDR.transfer($VALUE);
                ...
                $STATE = $NEW_VALUE;
                ...
              }
      # Exclude view functions and internal operations
      - pattern-not: |
          function $FUNC(...) $MODIFIERS view {
            ...
          }
      - pattern-not: |
          function $FUNC(...) $MODIFIERS pure {
            ...
          }
      # Exclude internal library calls and storage operations
      - pattern-not-inside: |
          function $FUNC(...) $MODIFIERS {
            ...
            $LIBRARY._$METHOD(...);
            ...
          }
    message: "State updates after external calls violate checks-effects-interactions pattern and may enable reentrancy."
    severity: ERROR
    languages: [solidity]
    metadata:
      category: security
      technology: solidity
      description: "State should be updated before making external calls to prevent reentrancy attacks."

  - id: missing-balance-validation
    patterns:
      - pattern-either:
          - pattern: |
              function withdraw(uint256 $AMOUNT) $VISIBILITY {
                ...
                $TOKEN.transfer(msg.sender, $AMOUNT);
                ...
              }
          - pattern: |
              function transfer(address $TO, uint256 $AMOUNT) $VISIBILITY {
                ...
                $BALANCE[$TO] = $BALANCE[$TO] + $AMOUNT;
                $BALANCE[msg.sender] = $BALANCE[msg.sender] - $AMOUNT;
                ...
              }
      - pattern-not: |
          function $FUNC(...) $VISIBILITY {
            require($BALANCE[msg.sender] >= $AMOUNT, ...);
            ...
          }
      - pattern-not: |
          function $FUNC(...) $VISIBILITY {
            require($AMOUNT <= $AVAILABLE, ...);
            ...
          }
    message: "Function '$FUNC' transfers tokens without validating sufficient balance. This can lead to underflow or failed transfers."
    severity: ERROR
    languages: [solidity]
    metadata:
      category: security
      technology: solidity
      description: "Token transfers should validate that the sender has sufficient balance before execution."

  - id: incorrect-loop-logic
    patterns:
      - pattern-either:
          - pattern: |
              for (uint256 $I = 0; $I <= $LENGTH; $I++) {
                ...
              }
          - pattern: |
              for (uint256 $I = 1; $I <= $ARRAY.length; $I++) {
                $ARRAY[$I] = $VALUE;
              }
      - pattern-not: |
          for (uint256 $I = 0; $I < $LENGTH; $I++) {
            ...
          }
    message: "Incorrect loop bounds can cause array out-of-bounds access or off-by-one errors."
    severity: ERROR
    languages: [solidity]
    metadata:
      category: security
      technology: solidity
      description: "Loop conditions should use < instead of <= to avoid accessing invalid array indices."

  - id: missing-zero-amount-check
    patterns:
      - pattern-either:
          - pattern: |
              function deposit(uint256 $AMOUNT) $VISIBILITY {
                ...
                $BALANCE[msg.sender] += $AMOUNT;
                ...
              }
          - pattern: |
              function mint(uint256 $AMOUNT) $VISIBILITY {
                ...
                _mint(msg.sender, $AMOUNT);
                ...
              }
      - pattern-not: |
          function $FUNC(uint256 $AMOUNT) $VISIBILITY {
            require($AMOUNT > 0, ...);
            ...
          }
    message: "Function '$FUNC' does not check for zero amount, which may lead to unnecessary gas consumption or logic errors."
    severity: WARNING
    languages: [solidity]
    metadata:
      category: security
      technology: solidity
      description: "Functions should validate that amounts are greater than zero to prevent unnecessary operations."

  - id: incorrect-comparison-operator
    patterns:
      - pattern-either:
          - pattern: |
              if ($BALANCE = $AMOUNT) {
                ...
              }
          - pattern: |
              require($BALANCE = $AMOUNT, ...);
    message: "Assignment operator (=) used instead of comparison operator (==). This will always assign and evaluate to true."
    severity: ERROR
    languages: [solidity]
    metadata:
      category: security
      technology: solidity
      description: "Assignment in conditional statements is likely a bug and should use comparison operators."

  - id: missing-return-value-check
    patterns:
      - pattern-either:
          - pattern: |
              $TOKEN.transfer($TO, $AMOUNT);
          - pattern: |
              $TOKEN.transferFrom($FROM, $TO, $AMOUNT);
      - pattern-not: |
          require($TOKEN.transfer($TO, $AMOUNT), ...);
      - pattern-not: |
          bool $SUCCESS = $TOKEN.transfer($TO, $AMOUNT);
          require($SUCCESS, ...);
    message: "ERC20 transfer return value not checked. Some tokens return false on failure instead of reverting."
    severity: WARNING
    languages: [solidity]
    metadata:
      category: security
      technology: solidity
      description: "ERC20 transfer functions should have their return values checked as some tokens don't revert on failure."

  - id: incorrect-event-emission-order
    patterns:
      - pattern: |
          function $FUNC(...) $MODIFIERS {
            ...
            emit $EVENT(...);
            ...
            $STATE = $NEW_VALUE;
            ...
          }
      # Only flag if this is a critical state change, not just any assignment
      - pattern-inside: |
          function $FUNC(...) $MODIFIERS {
            ...
            $BALANCE = $NEW_BALANCE;
            ...
          }
      - pattern-inside: |
          function $FUNC(...) $MODIFIERS {
            ...
            $OWNER = $NEW_OWNER;
            ...
          }
      # Exclude view functions
      - pattern-not: |
          function $FUNC(...) $MODIFIERS view {
            ...
          }
      - pattern-not: |
          function $FUNC(...) $MODIFIERS pure {
            ...
          }
    message: "Event emitted before critical state change. Events should reflect the final state after all changes."
    severity: WARNING
    languages: [solidity]
    metadata:
      category: security
      technology: solidity
      description: "Events should be emitted after state changes to accurately reflect the contract's state."

  - id: missing-deadline-validation-logic
    patterns:
      - pattern-either:
          # Only match functions with explicitly named deadline parameters
          - pattern: |
              function $FUNC(..., uint256 deadline) $VISIBILITY {
                ...
              }
          - pattern: |
              function $FUNC(..., uint256 expiry) $VISIBILITY {
                ...
              }
          - pattern: |
              function $FUNC(..., uint256 expiryTime) $VISIBILITY {
                ...
              }
          - pattern: |
              function $FUNC(..., uint256 validUntil) $VISIBILITY {
                ...
              }
      - pattern-not: |
          function $FUNC(..., uint256 deadline) $VISIBILITY {
            require(block.timestamp <= deadline, ...);
            ...
          }
      - pattern-not: |
          function $FUNC(..., uint256 expiry) $VISIBILITY {
            require(block.timestamp <= expiry, ...);
            ...
          }
      - pattern-not: |
          function $FUNC(..., uint256 $DEADLINE) $VISIBILITY {
            require(block.timestamp <= $DEADLINE, ...);
            ...
          }
    message: "Function '$FUNC' accepts deadline parameter but doesn't validate it against current timestamp."
    severity: WARNING
    languages: [solidity]
    metadata:
      category: security
      technology: solidity
      description: "Deadline parameters should be validated to ensure transactions are executed within the expected timeframe."

  - id: incorrect-fee-calculation-logic
    patterns:
      - pattern-either:
          - pattern: |
              uint256 $FEE = $AMOUNT * $FEE_RATE;
              uint256 $NET_AMOUNT = $AMOUNT - $FEE;
          - pattern: |
              uint256 $NET_AMOUNT = $AMOUNT * (10000 - $FEE_RATE);
      - pattern-not: |
          uint256 $FEE = $AMOUNT * $FEE_RATE / 10000;
          uint256 $NET_AMOUNT = $AMOUNT - $FEE;
    message: "Fee calculation logic appears incorrect. Ensure proper division by basis points (10000) or percentage (100)."
    severity: WARNING
    languages: [solidity]
    metadata:
      category: security
      technology: solidity
      description: "Fee calculations should properly handle basis points or percentages to avoid incorrect fee amounts."

  - id: missing-contract-existence-check
    patterns:
      - pattern-either:
          # Only flag low-level calls that are more dangerous
          - pattern: |
              $TARGET.call($DATA);
          - pattern: |
              $TARGET.delegatecall($DATA);
      - pattern-not-inside: |
          require($TARGET.code.length > 0, ...);
      - pattern-not-inside: |
          if ($TARGET.code.length == 0) revert(...);
      # Exclude known safe patterns
      - pattern-not-inside: |
          function $FUNC(...) $VISIBILITY view {
            ...
          }
    message: "Low-level call to address without checking if it's a contract. Calls to EOAs will succeed silently."
    severity: WARNING
    languages: [solidity]
    metadata:
      category: security
      technology: solidity
      description: "Low-level external calls should verify that the target address contains contract code."

  - id: incorrect-array-length-check
    patterns:
      - pattern-either:
          - pattern: |
              function $FUNC(address[] memory $ADDRESSES, uint256[] memory $AMOUNTS) $VISIBILITY {
                for (uint256 $I = 0; $I < $ADDRESSES.length; $I++) {
                  ...
                  $AMOUNTS[$I];
                  ...
                }
              }
          - pattern: |
              function $FUNC(uint256[] memory $ARRAY1, uint256[] memory $ARRAY2) $VISIBILITY {
                for (uint256 $I = 0; $I < $ARRAY1.length; $I++) {
                  ...
                  $ARRAY2[$I];
                  ...
                }
              }
      - pattern-not: |
          function $FUNC(...) $VISIBILITY {
            require($ADDRESSES.length == $AMOUNTS.length, ...);
            ...
          }
    message: "Function '$FUNC' uses multiple arrays without validating they have the same length. This can cause out-of-bounds access."
    severity: ERROR
    languages: [solidity]
    metadata:
      category: security
      technology: solidity
      description: "Functions using multiple arrays should validate that all arrays have the same length."

  - id: incorrect-reward-distribution-logic
    patterns:
      - pattern: |
          function distributeRewards() $VISIBILITY {
            for (uint256 $I = 0; $I < $USERS.length; $I++) {
              uint256 $REWARD = $TOTAL_REWARDS / $USERS.length;
              $REWARDS[$USERS[$I]] += $REWARD;
            }
          }
    message: "Equal reward distribution ignores user stakes or contributions. This may not be the intended behavior."
    severity: WARNING
    languages: [solidity]
    metadata:
      category: security
      technology: solidity
      description: "Reward distribution should typically be proportional to user stakes or contributions."
