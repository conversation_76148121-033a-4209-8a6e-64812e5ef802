rules:
  - id: missing-access-control-critical-functions
    patterns:
      - pattern-either:
          - pattern: |
              function $FUNC(...) external {
                ...
                $TOKEN.transfer(...);
                ...
              }
          - pattern: |
              function $FUNC(...) public {
                ...
                $TOKEN.transfer(...);
                ...
              }
          - pattern: |
              function $FUNC(...) external {
                ...
                selfdestruct(...);
                ...
              }
          - pattern: |
              function $FUNC(...) public {
                ...
                selfdestruct(...);
                ...
              }
      - pattern-not-inside: |
          modifier $MODIFIER {
            require(msg.sender == $OWNER, ...);
            _;
          }
      - pattern-not-inside: |
          modifier $MODIFIER {
            require(msg.sender == $AUTHORIZED, ...);
            _;
          }
      - pattern-not-inside: |
          modifier onlyOwner {
            ...
          }
      - pattern-not-inside: |
          modifier $MODIFIER {
            require(hasRole($ROLE, msg.sender), ...);
            _;
          }
      - pattern-not: |
          function $FUNC(...) $VISIBILITY {
            require(msg.sender == $OWNER, ...);
            ...
          }
      - pattern-not: |
          function $FUNC(...) $VISIBILITY {
            require(msg.sender == $AUTHORIZED, ...);
            ...
          }
      - pattern-not: |
          function $FUNC(...) $VISIBILITY {
            if (msg.sender != $OWNER) revert(...);
            ...
          }
      - pattern-not: |
          function $FUNC(...) $VISIBILITY {
            if (msg.sender != $AUTHORIZED) revert(...);
            ...
          }
      - pattern-not: |
          function $FUNC(...) $VISIBILITY {
            require(hasRole($ROLE, msg.sender), ...);
            ...
          }
      - pattern-not: |
          function $FUNC(...) $VISIBILITY onlyOwner {
            ...
          }
    message: "Critical function '$FUNC' lacks access control. Anyone can call this function and transfer tokens or destroy the contract."
    severity: ERROR
    languages: [solidity]
    metadata:
      category: security
      technology: solidity
      description: "Functions that transfer tokens or destroy contracts should have proper access control mechanisms."

  - id: weak-access-control-tx-origin
    patterns:
      - pattern-either:
          - pattern: |
              require(tx.origin == $OWNER, ...);
          - pattern: |
              if (tx.origin != $OWNER) revert(...);
          - pattern: |
              modifier $MODIFIER {
                require(tx.origin == $OWNER, ...);
                _;
              }
    message: "Using tx.origin for access control is vulnerable to phishing attacks. Use msg.sender instead."
    severity: ERROR
    languages: [solidity]
    metadata:
      category: security
      technology: solidity
      description: "tx.origin can be manipulated through phishing attacks where users are tricked into calling malicious contracts."

  - id: missing-owner-validation
    patterns:
      - pattern: |
          function transferOwnership(address $NEW_OWNER) $VISIBILITY {
            $OWNER = $NEW_OWNER;
          }
      - pattern-not: |
          function transferOwnership(address $NEW_OWNER) $VISIBILITY {
            require($NEW_OWNER != address(0), ...);
            $OWNER = $NEW_OWNER;
          }
    message: "transferOwnership function does not validate new owner address. Setting owner to zero address will lock the contract."
    severity: ERROR
    languages: [solidity]
    metadata:
      category: security
      technology: solidity
      description: "Owner transfer functions should validate that the new owner is not the zero address."

  - id: public-burn-function-without-access-control
    patterns:
      - pattern-either:
          - pattern: |
              function burn(address $FROM, uint256 $AMOUNT) public {
                ...
                _burn($FROM, $AMOUNT);
                ...
              }
          - pattern: |
              function burn(address $FROM, uint256 $AMOUNT) external {
                ...
                _burn($FROM, $AMOUNT);
                ...
              }
          - pattern: |
              function burnFrom(address $FROM, uint256 $AMOUNT) public {
                ...
                _burn($FROM, $AMOUNT);
                ...
              }
          - pattern: |
              function burnFrom(address $FROM, uint256 $AMOUNT) external {
                ...
                _burn($FROM, $AMOUNT);
                ...
              }
      - pattern-not: |
          function $FUNC(...) $VISIBILITY {
            require($CONDITION, ...);
            ...
          }
      - pattern-not: |
          function $FUNC(...) $VISIBILITY {
            require(msg.sender == $OWNER, ...);
            ...
          }
      - pattern-not-inside: |
          modifier onlyOwner {
            ...
          }
    message: "Public burn function allows burning tokens from arbitrary addresses without proper access control."
    severity: ERROR
    languages: [solidity]
    metadata:
      category: security
      technology: solidity
      description: "Public burn functions should have appropriate access controls or validation to prevent misuse."

  - id: arbitrary-external-call-without-validation
    patterns:
      - pattern-either:
          - pattern: |
              function $FUNC(address $TARGET, bytes calldata $DATA) external {
                ...
                $TARGET.call($DATA);
                ...
              }
          - pattern: |
              function $FUNC(address $TARGET, bytes memory $DATA) public {
                ...
                $TARGET.call($DATA);
                ...
              }
      - pattern-not: |
          function $FUNC(...) $VISIBILITY {
            require(msg.sender == $OWNER, ...);
            ...
          }
      - pattern-not: |
          function $FUNC(...) $VISIBILITY {
            require($WHITELIST[$TARGET], ...);
            ...
          }
    message: "Function '$FUNC' allows arbitrary external calls without proper access control or target validation."
    severity: ERROR
    languages: [solidity]
    metadata:
      category: security
      technology: solidity
      description: "Arbitrary external calls should be restricted to authorized users and validated targets."

  - id: missing-role-based-access-control
    patterns:
      - pattern-either:
          - pattern: |
              function mint(address $TO, uint256 $AMOUNT) external {
                ...
                _mint($TO, $AMOUNT);
                ...
              }
          - pattern: |
              function setPrice(uint256 $PRICE) external {
                ...
                $PRICE_VAR = $PRICE;
                ...
              }
      - pattern-not-inside: |
          modifier onlyRole(bytes32 $ROLE) {
            ...
          }
      - pattern-not-inside: |
          modifier onlyOwner {
            ...
          }
      - pattern-not: |
          function $FUNC(...) $VISIBILITY {
            require(hasRole($ROLE, msg.sender), ...);
            ...
          }
      - pattern-not: |
          function $FUNC(...) $VISIBILITY {
            require(msg.sender == $OWNER, ...);
            ...
          }
      - pattern-not: |
          function $FUNC(...) $VISIBILITY onlyOwner {
            ...
          }
    message: "Critical function lacks role-based access control. Consider implementing OpenZeppelin's AccessControl."
    severity: WARNING
    languages: [solidity]
    metadata:
      category: security
      technology: solidity
      description: "Critical functions should use role-based access control for better security and flexibility."

  - id: unprotected-initialize-function
    patterns:
      - pattern: |
          function initialize(...) $VISIBILITY {
            ...
          }
      - pattern-not: |
          function initialize(...) $VISIBILITY {
            require(!$INITIALIZED, ...);
            ...
          }
      - pattern-not-inside: |
          modifier initializer {
            ...
          }
    message: "Initialize function is not protected against multiple calls. This can lead to reinitialization attacks."
    severity: ERROR
    languages: [solidity]
    metadata:
      category: security
      technology: solidity
      description: "Initialize functions should be protected against multiple calls using initializer modifiers."

  - id: missing-multisig-for-critical-operations
    patterns:
      - pattern-either:
          - pattern: |
              function pause() external {
                require(msg.sender == $OWNER, ...);
                ...
              }
          - pattern: |
              function emergencyWithdraw() external {
                require(msg.sender == $OWNER, ...);
                ...
              }
          - pattern: |
              function upgradeContract(address $NEW_IMPL) external {
                require(msg.sender == $OWNER, ...);
                ...
              }
    message: "Critical operation controlled by single owner. Consider implementing multisig or timelock for better security."
    severity: WARNING
    languages: [solidity]
    metadata:
      category: security
      technology: solidity
      description: "Critical operations should require multiple signatures or time delays to prevent single points of failure."

  - id: delegatecall-without-access-control
    patterns:
      - pattern-either:
          - pattern: |
              function $FUNC(address $TARGET, bytes calldata $DATA) external {
                ...
                $TARGET.delegatecall($DATA);
                ...
              }
          - pattern: |
              function $FUNC(address $TARGET, bytes memory $DATA) public {
                ...
                $TARGET.delegatecall($DATA);
                ...
              }
      - pattern-not: |
          function $FUNC(...) $VISIBILITY {
            require(msg.sender == $OWNER, ...);
            ...
          }
    message: "delegatecall without access control allows arbitrary code execution in contract context."
    severity: ERROR
    languages: [solidity]
    metadata:
      category: security
      technology: solidity
      description: "delegatecall executes code in the current contract's context and should be heavily restricted."

  - id: missing-zero-address-check
    patterns:
      - pattern-either:
          - pattern: |
              function $FUNC(address $ADDR, ...) $VISIBILITY {
                ...
                $STATE_VAR = $ADDR;
                ...
              }
          - pattern: |
              function $FUNC(address $ADDR, ...) $VISIBILITY {
                ...
                $MAPPING[$KEY] = $ADDR;
                ...
              }
      - pattern-not: |
          function $FUNC(address $ADDR, ...) $VISIBILITY {
            require($ADDR != address(0), ...);
            ...
          }
      - pattern-not: |
          function $FUNC(address $ADDR, ...) $VISIBILITY {
            if ($ADDR == address(0)) revert(...);
            ...
          }
      - metavariable-regex:
          metavariable: $FUNC
          regex: .*(set|Set|add|Add|update|Update|transfer|Transfer|Owner|owner).*
      - metavariable-regex:
          metavariable: $STATE_VAR
          regex: .*(owner|Owner|admin|Admin|treasury|Treasury|recipient|Recipient).*
    message: "Function '$FUNC' sets address without zero address validation. This can lead to locked funds or broken functionality."
    severity: WARNING
    languages: [solidity]
    metadata:
      category: security
      technology: solidity
      description: "Address parameters should be validated against zero address to prevent accidental misconfigurations."

  - id: unprotected-withdrawal-function
    patterns:
      - pattern-either:
          - pattern: |
              function withdraw() external {
                ...
                payable(msg.sender).transfer(...);
                ...
              }
          - pattern: |
              function withdraw(uint256 $AMOUNT) external {
                ...
                $TOKEN.transfer(msg.sender, $AMOUNT);
                ...
              }
      - pattern-not: |
          function withdraw(...) $VISIBILITY {
            require($BALANCE[msg.sender] >= $AMOUNT, ...);
            ...
          }
      - pattern-not: |
          function withdraw(...) $VISIBILITY {
            require(msg.sender == $OWNER, ...);
            ...
          }
    message: "Withdrawal function lacks proper balance checks or access control. This may allow unauthorized withdrawals."
    severity: ERROR
    languages: [solidity]
    metadata:
      category: security
      technology: solidity
      description: "Withdrawal functions should verify user balances and implement proper access controls."
