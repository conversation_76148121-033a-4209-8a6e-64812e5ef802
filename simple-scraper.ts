import axios from "axios";
import fs from "fs/promises";
import path from "path";
import { immunefiLinks } from "./immunefi-links";

interface ExtractedContract {
  address: string;
  chain: string;
  originalUrl: string;
  bountyName: string;
}

class SimpleImmunefiScraper {
  private readonly outputDir = "immunefi-code";

  private delay(ms: number): Promise<void> {
    return new Promise((resolve) => setTimeout(resolve, ms));
  }

  async init() {
    // Create output directory if it doesn't exist
    await fs.mkdir(this.outputDir, { recursive: true });
  }

  private extractBountyName(url: string): string {
    // Remove trailing slash and extract bounty name
    const cleanUrl = url.replace(/\/$/, "");
    const match = /\/bounty\/([^\/]+)$/.exec(cleanUrl);
    return match ? match[1] : "unknown";
  }

  private extractAddressFromUrl(url: string): string | null {
    // Extract address from etherscan.io or bscscan.com URLs
    const patterns = [
      /\/address\/0x([a-fA-F0-9]{40})/,
      /\/token\/0x([a-fA-F0-9]{40})/,
      /0x([a-fA-F0-9]{40})/,
    ];

    for (const pattern of patterns) {
      const match = pattern.exec(url);
      if (match) {
        return "0x" + match[1];
      }
    }
    return null;
  }

  private determineChain(url: string): string {
    if (url.includes("bscscan.com")) return "bsc";
    if (url.includes("etherscan.io")) return "eth";
    if (url.includes("polygonscan.com")) return "polygon";
    if (url.includes("arbiscan.io")) return "arbitrum";
    if (url.includes("optimistic.etherscan.io")) return "optimism";
    if (url.includes("snowtrace.io")) return "avalanche";
    if (url.includes("ftmscan.com")) return "fantom";
    return "eth"; // default to ethereum
  }

  async scrapeBountyScope(bountyUrl: string): Promise<ExtractedContract[]> {
    const contracts: ExtractedContract[] = [];
    const bountyName = this.extractBountyName(bountyUrl);

    try {
      const scopeUrl = bountyUrl.endsWith("/")
        ? `${bountyUrl}scope`
        : `${bountyUrl}/scope`;
      console.log(`Scraping: ${scopeUrl}`);

      const response = await axios.get(scopeUrl, {
        timeout: 30000,
        headers: {
          "User-Agent":
            "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
          Accept:
            "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8",
          "Accept-Language": "en-US,en;q=0.5",
          "Accept-Encoding": "gzip, deflate",
          Connection: "keep-alive",
          "Upgrade-Insecure-Requests": "1",
        },
      });

      const html = response.data;

      // Extract all links that start with blockchain explorers
      const linkRegex =
        /href="([^"]*(?:etherscan\.io|bscscan\.com|polygonscan\.com|arbiscan\.io|optimistic\.etherscan\.io|snowtrace\.io|ftmscan\.com)[^"]*)"/g;
      const scannerLinks: string[] = [];
      let match;

      while ((match = linkRegex.exec(html)) !== null) {
        const link = match[1];
        if (!scannerLinks.includes(link)) {
          scannerLinks.push(link);
        }
      }

      console.log(
        `Found ${scannerLinks.length} scanner links for ${bountyName}`
      );

      // Extract addresses from scanner links
      for (const link of scannerLinks) {
        const address = this.extractAddressFromUrl(link);
        if (address) {
          const chain = this.determineChain(link);
          contracts.push({
            address,
            chain,
            originalUrl: link,
            bountyName,
          });
        }
      }
    } catch (error) {
      console.error(
        `Error scraping ${bountyUrl}:`,
        error instanceof Error ? error.message : String(error)
      );
    }

    return contracts;
  }

  async saveBlockSecLink(contract: ExtractedContract): Promise<void> {
    const blocksecUrl = `https://extension.blocksec.com/api/v1/source-code/download?chain=${contract.chain}&address=${contract.address}`;

    console.log(
      `Saving BlockSec link for ${contract.address} on ${contract.chain}`
    );

    // Create directory structure: immunefi-code/{bountyName}/{chain}/
    const contractDir = path.join(
      this.outputDir,
      contract.bountyName,
      contract.chain
    );
    await fs.mkdir(contractDir, { recursive: true });

    // Save metadata with BlockSec link
    const metadataPath = path.join(contractDir, `${contract.address}.json`);
    await fs.writeFile(
      metadataPath,
      JSON.stringify(
        {
          ...contract,
          blocksecDownloadUrl: blocksecUrl,
        },
        null,
        2
      )
    );

    console.log(`✓ Saved metadata: ${metadataPath}`);
  }

  async appendBlockSecLinksToFile(
    contracts: ExtractedContract[],
    filePath: string
  ): Promise<void> {
    const linksToAppend: string[] = [];

    for (const contract of contracts) {
      const blocksecUrl = `https://extension.blocksec.com/api/v1/source-code/download?chain=${contract.chain}&address=${contract.address}`;
      linksToAppend.push(
        `# ${contract.bountyName} - ${contract.chain} - ${contract.address}`
      );
      linksToAppend.push(blocksecUrl);
      linksToAppend.push(""); // Empty line for readability
    }

    const contentToAppend = linksToAppend.join("\n") + "\n";
    await fs.appendFile(filePath, contentToAppend);

    console.log(
      `✓ Appended ${contracts.length} BlockSec links to download file`
    );
  }

  async scrapeAll(): Promise<void> {
    console.log(
      `Starting to scrape ${immunefiLinks.length} bounty programs...`
    );

    // Remove duplicates from immunefiLinks
    const uniqueLinks = [...new Set(immunefiLinks)];
    console.log(`Processing ${uniqueLinks.length} unique bounty programs...`);

    const allContracts: ExtractedContract[] = [];

    // Initialize the download links file
    const downloadLinksPath = path.join(
      this.outputDir,
      "blocksec-download-links.txt"
    );
    const headerContent = [
      "# BlockSec Download Links for All Immunefi Contracts",
      `# Generated on: ${new Date().toISOString()}`,
      `# Total bounties to process: ${uniqueLinks.length}`,
      "",
      "# Usage: You can use these links with wget, curl, or any download manager",
      "# Example: wget -i blocksec-download-links.txt",
      "",
    ].join("\n");

    await fs.writeFile(downloadLinksPath, headerContent);
    console.log(`✓ Initialized download links file: ${downloadLinksPath}`);

    // Process bounties one by one to avoid rate limiting
    for (let i = 0; i < uniqueLinks.length; i++) {
      const url = uniqueLinks[i];

      console.log(`\nProcessing ${i + 1}/${uniqueLinks.length}: ${url}`);

      const contracts = await this.scrapeBountyScope(url);
      allContracts.push(...contracts);

      // Immediately write BlockSec links for this bounty to file
      if (contracts.length > 0) {
        await this.appendBlockSecLinksToFile(contracts, downloadLinksPath);
      }

      // Wait between requests to be respectful and avoid rate limiting
      if (i < uniqueLinks.length - 1) {
        console.log("Waiting 5 seconds before next request...");
        await this.delay(5000);
      }
    }

    console.log(
      `\nFound ${allContracts.length} total contracts across all bounties`
    );

    // Remove duplicate contracts (same address + chain combination)
    const uniqueContracts = allContracts.filter(
      (contract, index, self) =>
        index ===
        self.findIndex(
          (c) => c.address === contract.address && c.chain === contract.chain
        )
    );

    console.log(
      `After deduplication: ${uniqueContracts.length} unique contracts`
    );

    console.log("\n✓ Scraping completed!");
  }
}

// Main execution
async function main() {
  const scraper = new SimpleImmunefiScraper();

  try {
    await scraper.init();
    await scraper.scrapeAll();
  } catch (error) {
    console.error("Scraper failed:", error);
  }
}

// Run if this file is executed directly
if (require.main === module) {
  main().catch(console.error);
}

export { SimpleImmunefiScraper };
