{"name": "immunefi-all-repos", "version": "1.0.0", "description": "Scraper for Immunefi bounty programs to extract smart contract addresses and download source code", "license": "ISC", "author": "", "type": "commonjs", "main": "index.js", "scripts": {"test": "ts-node test-simple-scraper.ts", "scrape": "ts-node simple-scraper.ts", "scrape-browser": "ts-node immunefi-scraper.ts", "scrape-simple": "ts-node simple-scraper.ts", "test-simple": "ts-node test-simple-scraper.ts", "build": "tsc"}, "dependencies": {"puppeteer": "^21.5.2", "axios": "^1.6.2", "typescript": "^5.3.2", "ts-node": "^10.9.1", "@types/node": "^20.9.0"}, "devDependencies": {"@types/puppeteer": "^7.0.4"}}