#!/usr/bin/env bash
set -euo pipefail

INPUT_FILE="./immunefi-code/blocksec-download-links.txt"
OUT_DIR="./immunefi-code/out"
PARALLEL_JOBS=20  # Number of parallel downloads

mkdir -p "$OUT_DIR"

# Collect all URLs from the specific file
echo "Collecting links from $INPUT_FILE..."
URLS=$(grep -Eho 'https?://[^[:space:]]+' "$INPUT_FILE" | sort -u)

if [ -z "$URLS" ]; then
  echo "No URLs found."
  exit 0
fi

URL_COUNT=$(echo "$URLS" | wc -l)
echo "Found $URL_COUNT unique URL(s)."

# Function to download and extract a single URL
download_file() {
  local url="$1"
  local out_dir="$2"
  
  # Extract address from URL for filename
  address=$(echo "$url" | grep -o 'address=[^&]*' | cut -d'=' -f2)
  filename="${address:-unknown}.zip"
  
  echo "Downloading: $filename"
  
  # Download the file
  curl -s "$url" \
    -H 'accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7' \
    -H 'accept-language: en-US,en;q=0.9,ru-RU;q=0.8,ru;q=0.7,uk;q=0.6' \
    -H 'cache-control: no-cache' \
    -b 'AMP_5f4c1fb366=JTdCJTIyb3B0T3V0JTIyJTNBZmFsc2UlMkMlMjJkZXZpY2VJZCUyMiUzQSUyMmMxYzNhZjQwLTQwZjctNDFjMi05YjBmLTI0OGNkNWU3M2FjNSUyMiUyQyUyMmxhc3RFdmVudFRpbWUlMjIlM0ExNzU5MTM3NTgzNDI1JTJDJTIyc2Vzc2lvbklkJTIyJTNBMTc1OTEzNzU4MzM5MyUyQyUyMnVzZXJJZCUyMiUzQSUyMjNmMzhjMThkLTc2Y2QtNGYwNC1jMWMwLTc0NWY1ZmU5ZjFmZCUyMiU3RA==; AMP_MKTG_5f4c1fb366=JTdCJTIycmVmZXJyZXIlMjIlM0ElMjJodHRwcyUzQSUyRiUyRmV0aGVyc2Nhbi5pbyUyRiUyMiUyQyUyMnJlZmVycmluZ19kb21haW4lMjIlM0ElMjJldGhlcnNjYW4uaW8lMjIlN0Q=; cf_clearance=__mn5gxcZZjHMXnVF.aFnylqN3FF2A_HNRk6edPia64-1759137588-*******-FRyhkPavC2FzacuoFlsgEX9P.DMHypVMIXYN8VBd2a9dBCvNJUEovqJSmBwe3Gh3.AHadE8_BMru0XmElQhIfhjhZWzPrYLIhaeq.ZH8X2NaMhtM.2cE7htajYu2Lu0RvRRu_wPPibUcR1RXmuaKmNDHUTi1je2sFuz.INTNubpoUNpKg_.KDdKlw9anoR4pVvxhJhEsjmzXGYHB8IyI89khmUPJy7E4q4W_Ckmc8j4' \
    -H 'pragma: no-cache' \
    -H 'priority: u=0, i' \
    -H 'sec-ch-ua: "Chromium";v="140", "Not=A?Brand";v="24", "Google Chrome";v="140"' \
    -H 'sec-ch-ua-arch: "arm"' \
    -H 'sec-ch-ua-bitness: "64"' \
    -H 'sec-ch-ua-full-version: "140.0.7339.210"' \
    -H 'sec-ch-ua-full-version-list: "Chromium";v="140.0.7339.210", "Not=A?Brand";v="24.0.0.0", "Google Chrome";v="140.0.7339.210"' \
    -H 'sec-ch-ua-mobile: ?0' \
    -H 'sec-ch-ua-model: ""' \
    -H 'sec-ch-ua-platform: "macOS"' \
    -H 'sec-ch-ua-platform-version: "26.0.0"' \
    -H 'sec-fetch-dest: document' \
    -H 'sec-fetch-mode: navigate' \
    -H 'sec-fetch-site: none' \
    -H 'sec-fetch-user: ?1' \
    -H 'upgrade-insecure-requests: 1' \
    -H 'user-agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36' \
    -o "$out_dir/$filename" || {
    echo "  Failed to download: $filename ($url)"
    return 1
  }
  
  # Check if the downloaded file is a valid zip
  if [ -f "$out_dir/$filename" ]; then
    # Create a directory for this contract's files
    extract_dir="$out_dir/${filename%.zip}"
    mkdir -p "$extract_dir"
    
    # Extract the zip file
    if unzip -q "$out_dir/$filename" -d "$extract_dir" 2>/dev/null; then
      # Successfully extracted, remove the zip file
      rm "$out_dir/$filename"
      echo "  Extracted: ${filename%.zip}/"
    else
      # If unzip fails, check if it's actually a zip file
      if file "$out_dir/$filename" | grep -q "Zip archive"; then
        echo "  Failed to extract: $filename (corrupted zip)"
      else
        echo "  Not a zip file: $filename (keeping as-is)"
      fi
    fi
  else
    echo "  Failed to download: $filename"
    return 1
  fi
  
  return 0
}

# Export the function so it can be used by xargs
export -f download_file
export OUT_DIR

echo "Starting parallel downloads with $PARALLEL_JOBS concurrent jobs..."
echo "This will download $URL_COUNT files much faster than sequential processing."

# Use xargs to run downloads in parallel
echo "$URLS" | xargs -n 1 -P "$PARALLEL_JOBS" -I {} bash -c 'download_file "$1" "$2"' _ {} "$OUT_DIR"

echo "All done. Files extracted in $OUT_DIR/"
echo "Extracted directories:"
ls -1 "$OUT_DIR" | wc -l
echo ""
echo "Directory structure:"
ls -la "$OUT_DIR" | head -10
